import { types, flow, getRoot, Instance, SnapshotIn, cast } from "mobx-state-tree";
import { db, AppSetting } from "../services/DatabaseService"; // Assuming db service is set up
import { RootStoreType } from "./RootStore"; // Import RootStoreType for getRoot
import { availableCurves as crossfaderCurveOptions } from "../utils/crossfaderCurves";

// Define the available EQ frequency options
export const lowEQFrequencies = [60, 70, 100, 120, 180, 200, 300, 320, 330, 420];
export const midLoEQFrequencies = [250, 350, 420, 500, 1000, 1200, 1400, 1600];
export const midHiEQFrequencies = [1100, 1600, 2500, 2700, 3000, 3200, 4000, 4200];
export const highEQFrequencies = [3200, 4000, 6500, 10000, 13000, 16000];

// Define EQ presets for different devices/programs
export interface EQPreset {
  name: string;
  low: number;
  midLo?: number;
  midHi?: number;
  mid?: number;
  high: number;
  bands: 3 | 4;
}

// Define hardware emulation presets
export const eqHardwarePresets: EQPreset[] = [
  {
    name: "Pioneer DJM/rekordbox",
    low: 70,
    mid: 1000,
    high: 13000,
    bands: 3
  },
  {
    name: "Xone X:42",
    low: 420,
    mid: 1200,
    high: 2700,
    bands: 3
  },
  {
    name: "Xone X:4D",
    low: 120,
    mid: 1400,
    high: 10000,
    bands: 3
  },
  {
    name: "Rane",
    low: 300,
    mid: 1200,
    high: 4000,
    bands: 3
  },
  {
    name: "Evo4",
    low: 200,
    mid: 1200,
    high: 6500,
    bands: 3
  },
  {
    name: "DDM-4000",
    low: 330,
    mid: 1400,
    high: 4200,
    bands: 3
  },
  {
    name: "Virtual DJ",
    low: 200,
    mid: 1600,
    high: 6500,
    bands: 3
  },
  {
    name: "Xone X:92 (4-band)",
    low: 100,
    midLo: 250,
    midHi: 2500,
    high: 10000,
    bands: 4
  },
  {
    name: "Xone X:96 (4-band)",
    low: 180,
    midLo: 350,
    midHi: 1100,
    high: 3000,
    bands: 4
  }
];

// Define effect presets
export interface EQEffectPreset extends EQPreset {
  type: 'effect';
  lowGain?: number;
  midGain?: number;
  midLoGain?: number;
  midHiGain?: number;
  highGain?: number;
}

export const eqEffectPresets: EQEffectPreset[] = [
  {
    name: "Bass Boost",
    type: 'effect',
    low: 100,
    mid: 1000,
    high: 6500,
    bands: 3,
    lowGain: 6,
    midGain: 0,
    highGain: 0
  },
  {
    name: "Vocal Enhance",
    type: 'effect',
    low: 200,
    mid: 1000,
    high: 5000,
    bands: 3,
    lowGain: -3,
    midGain: 4,
    highGain: 2
  },
  {
    name: "Scooped Mids",
    type: 'effect',
    low: 200,
    mid: 1000,
    high: 5000,
    bands: 3,
    lowGain: 3,
    midGain: -6,
    highGain: 3
  },
  {
    name: "Hi-Fi",
    type: 'effect',
    low: 60,
    mid: 1000,
    high: 10000,
    bands: 3,
    lowGain: 4,
    midGain: -2,
    highGain: 5
  },
  {
    name: "Loudness",
    type: 'effect',
    low: 100,
    mid: 1000,
    high: 8000,
    bands: 3,
    lowGain: 6,
    midGain: 0,
    highGain: 6
  },
  {
    name: "Telephone",
    type: 'effect',
    low: 600,
    mid: 1500,
    high: 3000,
    bands: 3,
    lowGain: -12,
    midGain: 6,
    highGain: -12
  },
  {
    name: "Radio",
    type: 'effect',
    low: 500,
    mid: 1500,
    high: 4000,
    bands: 3,
    lowGain: -9,
    midGain: 3,
    highGain: -6
  },
  {
    name: "4-Band Bass Boost",
    type: 'effect',
    low: 100,
    midLo: 350,
    midHi: 2500,
    high: 8000,
    bands: 4,
    lowGain: 6,
    midLoGain: 2,
    midHiGain: 0,
    highGain: 0
  },
  {
    name: "4-Band Presence",
    type: 'effect',
    low: 100,
    midLo: 350,
    midHi: 2500,
    high: 8000,
    bands: 4,
    lowGain: 0,
    midLoGain: 0,
    midHiGain: 4,
    highGain: 2
  }
];

// Combine all presets for the UI
export const eqPresets: (EQPreset | EQEffectPreset)[] = [
  ...eqHardwarePresets,
  ...eqEffectPresets
];

// Extract just the names for the store
const availableCrossfaderCurveNames = crossfaderCurveOptions.map(curve => curve.name);

export const SettingsStoreModel = types
  .model("SettingsStore", {
    // Example settings - map structure might be better if many settings
    masterVolume: types.optional(types.number, 0.7),
    defaultPitchRange: types.optional(types.number, 8),
    // Add key notation setting
    useKeyNotation: types.optional(types.enumeration("KeyNotation", ["standard", "camelot"]), "standard"),
    // Add equalizer full kill setting
    equalizerFullKill: types.optional(types.boolean, false),
    // Add equalizer frequency settings
    lowEQFrequency: types.optional(types.number, 200), // Default to 200 Hz
    midEQFrequency: types.optional(types.number, 1600), // Default to 1600 Hz (for 3-band mode)
    midLoEQFrequency: types.optional(types.number, 350), // Default to 350 Hz (for 4-band mode)
    midHiEQFrequency: types.optional(types.number, 2500), // Default to 2500 Hz (for 4-band mode)
    highEQFrequency: types.optional(types.number, 6500), // Default to 6500 Hz
    // Add equalizer mode (3-band or 4-band)
    eqBands: types.optional(types.enumeration("EQBands", ["3-band", "4-band"]), "3-band"),
    // Add equalizer preset
    eqPreset: types.optional(types.string, "Custom"),
    // Add custom frequency input flags
    useCustomLowFreq: types.optional(types.boolean, false),
    useCustomMidFreq: types.optional(types.boolean, false),
    useCustomMidLoFreq: types.optional(types.boolean, false),
    useCustomMidHiFreq: types.optional(types.boolean, false),
    useCustomHighFreq: types.optional(types.boolean, false),
    // Add custom presets storage (serialized JSON string)
    customEQPresets: types.optional(types.string, "[]"),
    // Add last selected EQ preset for persistence
    lastSelectedEQPreset: types.optional(types.string, "Custom"),
    numberOfDecks: types.optional(types.union(types.literal(1), types.literal(2), types.literal(4)), 1),
    // Crossfader settings
    crossfaderValue: types.optional(types.number, 0.5),
    crossfaderCurve: types.optional(types.string, "Linear"),
    availableCrossfaderCurves: types.optional(types.array(types.string), () => cast(availableCrossfaderCurveNames)),
    // Per-band crossfader settings
    enablePerBandCrossfaders: types.optional(types.boolean, false),
    lowCrossfaderValue: types.optional(types.number, 0.5),
    midCrossfaderValue: types.optional(types.number, 0.5), // Used in 3-band mode
    // Master Deck and Sync settings
    autoMasterMode: types.optional(types.boolean, false),
    syncGranularity: types.optional(types.enumeration("SyncGranularity", ["beat", "half-beat", "quarter-beat"]), "beat"),
    maxSyncPitchRange: types.optional(types.enumeration("MaxSyncPitchRange", ["10", "20", "unlimited"]), "20"), // Percentage or unlimited
    midLoCrossfaderValue: types.optional(types.number, 0.5), // Used in 4-band mode
    midHiCrossfaderValue: types.optional(types.number, 0.5), // Used in 4-band mode
    highCrossfaderValue: types.optional(types.number, 0.5),
    // Audio routing and output settings
    masterOutputDeviceId: types.optional(types.string, "default"),
    headphoneOutputDeviceId: types.optional(types.string, "default"),
    headphoneVolume: types.optional(types.number, 0.5),
    masterHeadphoneMonitoring: types.optional(types.boolean, false),
    // Deck headphone monitoring (per deck)
    deckHeadphoneMonitoring: types.optional(types.map(types.boolean), () => new Map()),
  })
  .actions((self) => ({
    // Example action to update a setting and persist it
    setMasterVolume: flow(function* (volume: number) {
      self.masterVolume = volume;
      try {
        yield db.saveSetting({ key: 'masterVolume', value: volume });
      } catch (error) {
        console.error("Failed to save masterVolume setting:", error);
        // Optionally revert state or show error to user
      }
    }),
    setDefaultPitchRange: flow(function* (range: number) {
        self.defaultPitchRange = range;
        try {
            yield db.saveSetting({ key: 'defaultPitchRange', value: range });
        } catch (error) {
            console.error("Failed to save defaultPitchRange setting:", error);
        }
    }),
    // Add action to update key notation setting
    setKeyNotation: flow(function* (notation: "standard" | "camelot") {
        self.useKeyNotation = notation;
        try {
            yield db.saveSetting({ key: 'useKeyNotation', value: notation });
        } catch (error) {
            console.error("Failed to save useKeyNotation setting:", error);
        }
    }),

    // Action to update equalizer full kill setting
    setEqualizerFullKill: flow(function* (enabled: boolean) {
        self.equalizerFullKill = enabled;
        try {
            yield db.saveSetting({ key: 'equalizerFullKill', value: enabled });
        } catch (error) {
            console.error("Failed to save equalizerFullKill setting:", error);
        }
    }),

    // Actions to update EQ frequency settings
    setLowEQFrequency: flow(function* (frequency: number) {
        self.lowEQFrequency = frequency;
        self.eqPreset = "Custom"; // Set to custom when manually changing frequencies
        try {
            yield db.saveSetting({ key: 'lowEQFrequency', value: frequency });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save lowEQFrequency setting:", error);
        }
    }),

    setMidEQFrequency: flow(function* (frequency: number) {
        self.midEQFrequency = frequency;
        self.eqPreset = "Custom"; // Set to custom when manually changing frequencies
        try {
            yield db.saveSetting({ key: 'midEQFrequency', value: frequency });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save midEQFrequency setting:", error);
        }
    }),

    setMidLoEQFrequency: flow(function* (frequency: number) {
        self.midLoEQFrequency = frequency;
        self.eqPreset = "Custom"; // Set to custom when manually changing frequencies
        try {
            yield db.saveSetting({ key: 'midLoEQFrequency', value: frequency });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save midLoEQFrequency setting:", error);
        }
    }),

    setMidHiEQFrequency: flow(function* (frequency: number) {
        self.midHiEQFrequency = frequency;
        self.eqPreset = "Custom"; // Set to custom when manually changing frequencies
        try {
            yield db.saveSetting({ key: 'midHiEQFrequency', value: frequency });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save midHiEQFrequency setting:", error);
        }
    }),

    setHighEQFrequency: flow(function* (frequency: number) {
        self.highEQFrequency = frequency;
        self.eqPreset = "Custom"; // Set to custom when manually changing frequencies
        try {
            yield db.saveSetting({ key: 'highEQFrequency', value: frequency });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save highEQFrequency setting:", error);
        }
    }),

    setEQBands: flow(function* (bands: "3-band" | "4-band") {
        self.eqBands = bands;
        try {
            yield db.saveSetting({ key: 'eqBands', value: bands });
            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save eqBands setting:", error);
        }
    }),

    setEQPreset: flow(function* (presetName: string) {
        self.eqPreset = presetName;

        // Also update the last selected preset
        self.lastSelectedEQPreset = presetName;

        // Find the preset
        const preset = eqPresets.find(p => p.name === presetName);
        if (!preset) return;

        // Apply the preset frequencies
        self.lowEQFrequency = preset.low;

        if (preset.bands === 3) {
            self.eqBands = "3-band";
            if (preset.mid) self.midEQFrequency = preset.mid;
        } else {
            self.eqBands = "4-band";
            if (preset.midLo) self.midLoEQFrequency = preset.midLo;
            if (preset.midHi) self.midHiEQFrequency = preset.midHi;
        }

        self.highEQFrequency = preset.high;

        try {
            // Save all the settings
            yield db.saveSetting({ key: 'eqPreset', value: presetName });
            yield db.saveSetting({ key: 'lastSelectedEQPreset', value: presetName });
            yield db.saveSetting({ key: 'eqBands', value: self.eqBands });
            yield db.saveSetting({ key: 'lowEQFrequency', value: self.lowEQFrequency });

            if (self.eqBands === "3-band") {
                yield db.saveSetting({ key: 'midEQFrequency', value: self.midEQFrequency });
            } else {
                yield db.saveSetting({ key: 'midLoEQFrequency', value: self.midLoEQFrequency });
                yield db.saveSetting({ key: 'midHiEQFrequency', value: self.midHiEQFrequency });
            }

            yield db.saveSetting({ key: 'highEQFrequency', value: self.highEQFrequency });

            // Update all deck EQ frequencies
            const rootStore = getRoot<RootStoreType>(self);
            rootStore.updateAllDeckEQFrequencies();
        } catch (error) {
            console.error("Failed to save EQ preset settings:", error);
        }
    }),

    // Actions for custom frequency input flags
    setUseCustomLowFreq: flow(function* (value: boolean) {
        self.useCustomLowFreq = value;
        try {
            yield db.saveSetting({ key: 'useCustomLowFreq', value });
        } catch (error) {
            console.error("Failed to save useCustomLowFreq setting:", error);
        }
    }),

    setUseCustomMidFreq: flow(function* (value: boolean) {
        self.useCustomMidFreq = value;
        try {
            yield db.saveSetting({ key: 'useCustomMidFreq', value });
        } catch (error) {
            console.error("Failed to save useCustomMidFreq setting:", error);
        }
    }),

    setUseCustomMidLoFreq: flow(function* (value: boolean) {
        self.useCustomMidLoFreq = value;
        try {
            yield db.saveSetting({ key: 'useCustomMidLoFreq', value });
        } catch (error) {
            console.error("Failed to save useCustomMidLoFreq setting:", error);
        }
    }),

    setUseCustomMidHiFreq: flow(function* (value: boolean) {
        self.useCustomMidHiFreq = value;
        try {
            yield db.saveSetting({ key: 'useCustomMidHiFreq', value });
        } catch (error) {
            console.error("Failed to save useCustomMidHiFreq setting:", error);
        }
    }),

    setUseCustomHighFreq: flow(function* (value: boolean) {
        self.useCustomHighFreq = value;
        try {
            yield db.saveSetting({ key: 'useCustomHighFreq', value });
        } catch (error) {
            console.error("Failed to save useCustomHighFreq setting:", error);
        }
    }),

    // Save a new custom preset
    saveCustomEQPreset: flow(function* (preset: EQEffectPreset) {
        try {
            // Parse current custom presets
            let customPresets: EQEffectPreset[] = [];
            try {
                customPresets = JSON.parse(self.customEQPresets) as EQEffectPreset[];
            } catch (parseError) {
                console.error("Failed to parse custom EQ presets:", parseError);
                customPresets = [];
            }

            // Check if a preset with this name already exists
            const existingIndex = customPresets.findIndex((p: EQEffectPreset) => p.name === preset.name);

            if (existingIndex >= 0) {
                // Update existing preset
                customPresets[existingIndex] = preset;
            } else {
                // Add new preset
                customPresets.push(preset);
            }

            // Update the store
            self.customEQPresets = JSON.stringify(customPresets);

            // Save to database
            yield db.saveSetting({ key: 'customEQPresets', value: self.customEQPresets });

            console.log(`Custom EQ preset "${preset.name}" saved successfully`);
            return true;
        } catch (error) {
            console.error("Failed to save custom EQ preset:", error);
            return false;
        }
    }),

    // Delete a custom preset
    deleteCustomEQPreset: flow(function* (presetName: string) {
        try {
            // Parse current custom presets
            let customPresets: EQEffectPreset[] = [];
            try {
                customPresets = JSON.parse(self.customEQPresets) as EQEffectPreset[];
            } catch (parseError) {
                console.error("Failed to parse custom EQ presets:", parseError);
                customPresets = [];
            }

            // Filter out the preset to delete
            customPresets = customPresets.filter((p: EQEffectPreset) => p.name !== presetName);

            // Update the store
            self.customEQPresets = JSON.stringify(customPresets);

            // Save to database
            yield db.saveSetting({ key: 'customEQPresets', value: self.customEQPresets });

            console.log(`Custom EQ preset "${presetName}" deleted successfully`);
            return true;
        } catch (error) {
            console.error("Failed to delete custom EQ preset:", error);
            return false;
        }
    }),

    // Get all custom presets
    getCustomEQPresets(): EQEffectPreset[] {
        try {
            return JSON.parse(self.customEQPresets) as EQEffectPreset[];
        } catch (error) {
            console.error("Failed to parse custom EQ presets:", error);
            return [];
        }
    },

    // Set the last selected EQ preset
    setLastSelectedEQPreset: flow(function* (presetName: string) {
        self.lastSelectedEQPreset = presetName;
        try {
            yield db.saveSetting({ key: 'lastSelectedEQPreset', value: presetName });
        } catch (error) {
            console.error("Failed to save lastSelectedEQPreset setting:", error);
        }
    }),

    setNumberOfDecks: flow(function* (newNumberOfDecks: 1 | 2 | 4) {
      self.numberOfDecks = newNumberOfDecks;
      try {
        yield db.saveSetting({ key: 'numberOfDecks', value: newNumberOfDecks });
        const rootStore = getRoot<RootStoreType>(self);
        rootStore.updateDeckInstances(newNumberOfDecks);
      } catch (error) {
        console.error("Failed to save numberOfDecks setting:", error);
        // Optionally revert state or show error to user
      }
    }),

    // Crossfader actions
    setCrossfaderValue: flow(function* (value: number) {
      self.crossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'crossfaderValue', value: self.crossfaderValue });
      } catch (error) {
        console.error("Failed to save crossfaderValue setting:", error);
      }
    }),

    setCrossfaderCurve: flow(function* (curveName: string) {
      if (self.availableCrossfaderCurves.includes(curveName)) {
        self.crossfaderCurve = curveName;
        try {
          yield db.saveSetting({ key: 'crossfaderCurve', value: curveName });
        } catch (error) {
          console.error("Failed to save crossfaderCurve setting:", error);
        }
      } else {
        console.warn(`Attempted to set an unknown crossfader curve: ${curveName}`);
      }
    }),

    // Per-band crossfader actions
    setEnablePerBandCrossfaders: flow(function* (enabled: boolean) {
      self.enablePerBandCrossfaders = enabled;
      try {
        yield db.saveSetting({ key: 'enablePerBandCrossfaders', value: enabled });
      } catch (error) {
        console.error("Failed to save enablePerBandCrossfaders setting:", error);
      }
    }),

    setLowCrossfaderValue: flow(function* (value: number) {
      self.lowCrossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'lowCrossfaderValue', value: self.lowCrossfaderValue });
      } catch (error) {
        console.error("Failed to save lowCrossfaderValue setting:", error);
      }
    }),

    setMidCrossfaderValue: flow(function* (value: number) {
      self.midCrossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'midCrossfaderValue', value: self.midCrossfaderValue });
      } catch (error) {
        console.error("Failed to save midCrossfaderValue setting:", error);
      }
    }),

    setMidLoCrossfaderValue: flow(function* (value: number) {
      self.midLoCrossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'midLoCrossfaderValue', value: self.midLoCrossfaderValue });
      } catch (error) {
        console.error("Failed to save midLoCrossfaderValue setting:", error);
      }
    }),

    setMidHiCrossfaderValue: flow(function* (value: number) {
      self.midHiCrossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'midHiCrossfaderValue', value: self.midHiCrossfaderValue });
      } catch (error) {
        console.error("Failed to save midHiCrossfaderValue setting:", error);
      }
    }),

    setHighCrossfaderValue: flow(function* (value: number) {
      self.highCrossfaderValue = Math.max(0, Math.min(1, value)); // Clamp value between 0 and 1
      try {
        yield db.saveSetting({ key: 'highCrossfaderValue', value: self.highCrossfaderValue });
      } catch (error) {
        console.error("Failed to save highCrossfaderValue setting:", error);
      }
    }),

    // Master Deck and Sync actions
    setAutoMasterMode: flow(function* (enabled: boolean) {
      self.autoMasterMode = enabled;
      try {
        yield db.saveSetting({ key: 'autoMasterMode', value: enabled });
      } catch (error) {
        console.error("Failed to save autoMasterMode setting:", error);
      }
    }),

    setSyncGranularity: flow(function* (granularity: "beat" | "half-beat" | "quarter-beat") {
      self.syncGranularity = granularity;
      try {
        yield db.saveSetting({ key: 'syncGranularity', value: granularity });
      } catch (error) {
        console.error("Failed to save syncGranularity setting:", error);
      }
    }),

    setMaxSyncPitchRange: flow(function* (range: "10" | "20" | "unlimited") {
      self.maxSyncPitchRange = range;
      try {
        yield db.saveSetting({ key: 'maxSyncPitchRange', value: range });
      } catch (error) {
        console.error("Failed to save maxSyncPitchRange setting:", error);
      }
    }),

    // Audio routing and output actions
    setMasterOutputDeviceId: flow(function* (deviceId: string) {
      self.masterOutputDeviceId = deviceId;
      try {
        yield db.saveSetting({ key: 'masterOutputDeviceId', value: deviceId });
      } catch (error) {
        console.error("Failed to save masterOutputDeviceId setting:", error);
      }
    }),

    setHeadphoneOutputDeviceId: flow(function* (deviceId: string) {
      self.headphoneOutputDeviceId = deviceId;
      try {
        yield db.saveSetting({ key: 'headphoneOutputDeviceId', value: deviceId });
      } catch (error) {
        console.error("Failed to save headphoneOutputDeviceId setting:", error);
      }
    }),

    setHeadphoneVolume: flow(function* (volume: number) {
      self.headphoneVolume = Math.max(0, Math.min(1, volume));
      try {
        yield db.saveSetting({ key: 'headphoneVolume', value: self.headphoneVolume });
      } catch (error) {
        console.error("Failed to save headphoneVolume setting:", error);
      }
    }),

    setMasterHeadphoneMonitoring: flow(function* (enabled: boolean) {
      self.masterHeadphoneMonitoring = enabled;
      try {
        yield db.saveSetting({ key: 'masterHeadphoneMonitoring', value: enabled });
      } catch (error) {
        console.error("Failed to save masterHeadphoneMonitoring setting:", error);
      }
    }),

    setDeckHeadphoneMonitoring: flow(function* (deckId: string, enabled: boolean) {
      self.deckHeadphoneMonitoring.set(deckId, enabled);
      try {
        // Save the entire map as JSON
        const monitoringMap = Object.fromEntries(self.deckHeadphoneMonitoring);
        yield db.saveSetting({ key: 'deckHeadphoneMonitoring', value: JSON.stringify(monitoringMap) });
      } catch (error) {
        console.error("Failed to save deckHeadphoneMonitoring setting:", error);
      }
    }),

    // Action to load settings from DB on startup
    hydrateFromDb: flow(function* () {
      try {
        const settings: AppSetting[] = yield db.getAllSettings();
        settings.forEach(setting => {
          if (setting.key === 'masterVolume' && typeof setting.value === 'number') {
            self.masterVolume = setting.value;
          } else if (setting.key === 'defaultPitchRange' && typeof setting.value === 'number') {
            self.defaultPitchRange = setting.value;
          } else if (setting.key === 'useKeyNotation' &&
                    (setting.value === 'standard' || setting.value === 'camelot')) {
            self.useKeyNotation = setting.value;
          } else if (setting.key === 'equalizerFullKill' && typeof setting.value === 'boolean') {
            self.equalizerFullKill = setting.value;
          } else if (setting.key === 'eqBands' &&
                    (setting.value === '3-band' || setting.value === '4-band')) {
            self.eqBands = setting.value;
          } else if (setting.key === 'eqPreset' && typeof setting.value === 'string') {
            self.eqPreset = setting.value;
          } else if (setting.key === 'lowEQFrequency' && typeof setting.value === 'number') {
            self.lowEQFrequency = setting.value;
          } else if (setting.key === 'midEQFrequency' && typeof setting.value === 'number') {
            self.midEQFrequency = setting.value;
          } else if (setting.key === 'midLoEQFrequency' && typeof setting.value === 'number') {
            self.midLoEQFrequency = setting.value;
          } else if (setting.key === 'midHiEQFrequency' && typeof setting.value === 'number') {
            self.midHiEQFrequency = setting.value;
          } else if (setting.key === 'highEQFrequency' && typeof setting.value === 'number') {
            self.highEQFrequency = setting.value;
          } else if (setting.key === 'useCustomLowFreq' && typeof setting.value === 'boolean') {
            self.useCustomLowFreq = setting.value;
          } else if (setting.key === 'useCustomMidFreq' && typeof setting.value === 'boolean') {
            self.useCustomMidFreq = setting.value;
          } else if (setting.key === 'useCustomMidLoFreq' && typeof setting.value === 'boolean') {
            self.useCustomMidLoFreq = setting.value;
          } else if (setting.key === 'useCustomMidHiFreq' && typeof setting.value === 'boolean') {
            self.useCustomMidHiFreq = setting.value;
          } else if (setting.key === 'useCustomHighFreq' && typeof setting.value === 'boolean') {
            self.useCustomHighFreq = setting.value;
          } else if (setting.key === 'customEQPresets' && typeof setting.value === 'string') {
            self.customEQPresets = setting.value;
          } else if (setting.key === 'lastSelectedEQPreset' && typeof setting.value === 'string') {
            self.lastSelectedEQPreset = setting.value;
            // If we have a last selected preset, set it as the current preset
            if (setting.value !== 'Custom') {
              self.eqPreset = setting.value;
            }
          } else if (setting.key === 'numberOfDecks' &&
                   (setting.value === 1 || setting.value === 2 || setting.value === 4)) {
          self.numberOfDecks = setting.value;
        } else if (setting.key === 'crossfaderValue' && typeof setting.value === 'number') {
          self.crossfaderValue = setting.value;
        } else if (setting.key === 'crossfaderCurve' && typeof setting.value === 'string' && self.availableCrossfaderCurves.includes(setting.value)) {
          self.crossfaderCurve = setting.value;
        } else if (setting.key === 'enablePerBandCrossfaders' && typeof setting.value === 'boolean') {
          self.enablePerBandCrossfaders = setting.value;
        } else if (setting.key === 'lowCrossfaderValue' && typeof setting.value === 'number') {
          self.lowCrossfaderValue = setting.value;
        } else if (setting.key === 'midCrossfaderValue' && typeof setting.value === 'number') {
          self.midCrossfaderValue = setting.value;
        } else if (setting.key === 'midLoCrossfaderValue' && typeof setting.value === 'number') {
          self.midLoCrossfaderValue = setting.value;
        } else if (setting.key === 'midHiCrossfaderValue' && typeof setting.value === 'number') {
          self.midHiCrossfaderValue = setting.value;
        } else if (setting.key === 'highCrossfaderValue' && typeof setting.value === 'number') {
          self.highCrossfaderValue = setting.value;
        } else if (setting.key === 'autoMasterMode' && typeof setting.value === 'boolean') {
          self.autoMasterMode = setting.value;
        } else if (setting.key === 'syncGranularity' &&
                  (setting.value === 'beat' || setting.value === 'half-beat' || setting.value === 'quarter-beat')) {
          self.syncGranularity = setting.value;
        } else if (setting.key === 'maxSyncPitchRange' &&
                  (setting.value === '10' || setting.value === '20' || setting.value === 'unlimited')) {
          self.maxSyncPitchRange = setting.value;
        } else if (setting.key === 'masterOutputDeviceId' && typeof setting.value === 'string') {
          self.masterOutputDeviceId = setting.value;
        } else if (setting.key === 'headphoneOutputDeviceId' && typeof setting.value === 'string') {
          self.headphoneOutputDeviceId = setting.value;
        } else if (setting.key === 'headphoneVolume' && typeof setting.value === 'number') {
          self.headphoneVolume = setting.value;
        } else if (setting.key === 'masterHeadphoneMonitoring' && typeof setting.value === 'boolean') {
          self.masterHeadphoneMonitoring = setting.value;
        } else if (setting.key === 'deckHeadphoneMonitoring' && typeof setting.value === 'string') {
          try {
            const monitoringMap = JSON.parse(setting.value);
            self.deckHeadphoneMonitoring.clear();
            Object.entries(monitoringMap).forEach(([deckId, enabled]) => {
              if (typeof enabled === 'boolean') {
                self.deckHeadphoneMonitoring.set(deckId, enabled);
              }
            });
          } catch (error) {
            console.error("Failed to parse deckHeadphoneMonitoring setting:", error);
          }
        }
          // Add other settings here
        });
        console.log("SettingsStore hydrated from DB.");
      } catch (error) {
        console.error("Failed to hydrate SettingsStore from DB:", error);
      }
    }),
  }));

export type SettingsStoreType = Instance<typeof SettingsStoreModel>;
export type SettingsStoreSnapshotType = SnapshotIn<typeof SettingsStoreModel>;

