// src/components/Mixer/MasterVolumeControl.tsx

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Label } from '../ui/label';
import { Fader } from '../ui/fader';
import { cn } from '../../lib/utils';

export const MasterVolumeControl: React.FC = observer(() => {
  const rootStore = useStore();
  const { settingsStore } = rootStore;
  const [masterLevel, setMasterLevel] = useState(0);
  const [peakLevel, setPeakLevel] = useState(0);

  const handleMasterVolumeChange = (value: number) => {
    settingsStore.setMasterVolume(value);
    
    // Update the audio routing manager
    try {
      const masterPath = rootStore.audioRoutingManager.getMasterPath();
      masterPath.setMasterVolume(value);
    } catch (error) {
      console.error('Failed to update master volume:', error);
    }
  };

  // Monitor master audio levels
  useEffect(() => {
    let animationFrameId: number;
    
    const updateLevels = () => {
      try {
        const masterPath = rootStore.audioRoutingManager.getMasterPath();
        const analyzer = masterPath.getMasterAnalyzer();
        
        const rms = analyzer.getRMS();
        const peak = analyzer.getPeak();
        
        setMasterLevel(rms);
        setPeakLevel(peak);
        
        animationFrameId = requestAnimationFrame(updateLevels);
      } catch (error) {
        // AudioRoutingManager might not be initialized yet
        animationFrameId = requestAnimationFrame(updateLevels);
      }
    };
    
    updateLevels();
    
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [rootStore.audioRoutingManager]);

  // Convert linear values to dB for display
  const masterLeveldB = masterLevel > 0 ? 20 * Math.log10(masterLevel) : -Infinity;
  const peakLeveldB = peakLevel > 0 ? 20 * Math.log10(peakLevel) : -Infinity;

  // Calculate level bar heights (0-100%)
  const masterLevelPercent = Math.max(0, Math.min(100, (masterLeveldB + 60) / 60 * 100));
  const peakLevelPercent = Math.max(0, Math.min(100, (peakLeveldB + 60) / 60 * 100));

  // Determine level colors
  const getLevelColor = (dB: number) => {
    if (dB > -6) return 'bg-red-500'; // Red zone (clipping)
    if (dB > -12) return 'bg-yellow-500'; // Yellow zone (loud)
    if (dB > -24) return 'bg-green-500'; // Green zone (good)
    return 'bg-green-300'; // Low level
  };

  return (
    <Card className={cn("p-4 rounded-lg shadow-md bg-card text-card-foreground flex flex-col space-y-4 w-full max-w-md mx-auto")}>
      <div className="w-full text-center">
        <h3 className="text-lg font-semibold">Master Volume</h3>
      </div>

      {/* Master Volume Fader */}
      <div className="space-y-2">
        <Label htmlFor="master-volume" className="text-sm font-medium">
          Master Volume
        </Label>
        <div className="px-2">
          <Fader
            id="master-volume"
            orientation="horizontal"
            value={settingsStore.masterVolume}
            onChange={handleMasterVolumeChange}
            min={0}
            max={1}
            step={0.01}
            className="w-full"
            enableDoubleClick={true}
          />
        </div>
        <div className="text-xs text-muted-foreground text-center">
          {Math.round(settingsStore.masterVolume * 100)}%
        </div>
      </div>

      {/* Audio Level Meters */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Audio Levels</Label>
        
        <div className="flex items-center space-x-2">
          {/* RMS Level Meter */}
          <div className="flex-1">
            <div className="text-xs text-muted-foreground mb-1">RMS</div>
            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={cn("h-full transition-all duration-75", getLevelColor(masterLeveldB))}
                style={{ width: `${masterLevelPercent}%` }}
              />
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {isFinite(masterLeveldB) ? `${masterLeveldB.toFixed(1)} dB` : '-∞ dB'}
            </div>
          </div>

          {/* Peak Level Meter */}
          <div className="flex-1">
            <div className="text-xs text-muted-foreground mb-1">Peak</div>
            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={cn("h-full transition-all duration-75", getLevelColor(peakLeveldB))}
                style={{ width: `${peakLevelPercent}%` }}
              />
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {isFinite(peakLeveldB) ? `${peakLeveldB.toFixed(1)} dB` : '-∞ dB'}
            </div>
          </div>
        </div>

        {/* Level Scale Reference */}
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>-60dB</span>
          <span>-24dB</span>
          <span>-12dB</span>
          <span>-6dB</span>
          <span>0dB</span>
        </div>
      </div>

      {/* Clipping Warning */}
      {peakLeveldB > -3 && (
        <div className="text-xs text-red-500 bg-red-50 p-2 rounded text-center">
          ⚠️ Audio clipping detected! Reduce master volume.
        </div>
      )}
    </Card>
  );
});

export default MasterVolumeControl;
